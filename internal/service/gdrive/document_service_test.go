package gdrive

import (
	"context"
	"errors"
	"testing"
	"time"

	"bilabl/docman/domain/model"
	mockRepos "bilabl/docman/mocks/repositories"
	mockGdrive "bilabl/docman/mocks/gdrive"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/api/drive/v3"
	"gorm.io/gorm"
)

func TestValidateDocumentName(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid name",
			input:       "Valid Document Name",
			expectError: false,
		},
		{
			name:        "empty name",
			input:       "",
			expectError: true,
			errorMsg:    "document name cannot be empty",
		},
		{
			name:        "name too long",
			input:       string(make([]byte, MaxDocumentNameLength+1)),
			expectError: true,
			errorMsg:    "document name exceeds maximum length",
		},
		{
			name:        "invalid character slash",
			input:       "Invalid/Name",
			expectError: true,
			errorMsg:    "document name contains invalid character: /",
		},
		{
			name:        "invalid character backslash",
			input:       "Invalid\\Name",
			expectError: true,
			errorMsg:    "document name contains invalid character: \\",
		},
		{
			name:        "path traversal attempt",
			input:       "../malicious",
			expectError: true,
			errorMsg:    "document name contains invalid path traversal pattern",
		},
		{
			name:        "leading whitespace",
			input:       " Invalid Name",
			expectError: true,
			errorMsg:    "document name cannot have leading or trailing whitespace",
		},
		{
			name:        "trailing whitespace",
			input:       "Invalid Name ",
			expectError: true,
			errorMsg:    "document name cannot have leading or trailing whitespace",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateDocumentName(tt.input)
			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateCreateDocumentRequest(t *testing.T) {
	tests := []struct {
		name        string
		request     *CreateDocumentRequest
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid request",
			request: &CreateDocumentRequest{
				TenantID: 1,
				Name:     "Valid Document",
				DocType:  DocTypeDir,
			},
			expectError: false,
		},
		{
			name:        "nil request",
			request:     nil,
			expectError: true,
			errorMsg:    "request cannot be nil",
		},
		{
			name: "missing tenant ID",
			request: &CreateDocumentRequest{
				Name:    "Valid Document",
				DocType: DocTypeDir,
			},
			expectError: true,
			errorMsg:    "tenant ID is required",
		},
		{
			name: "invalid document name",
			request: &CreateDocumentRequest{
				TenantID: 1,
				Name:     "Invalid/Name",
				DocType:  DocTypeDir,
			},
			expectError: true,
			errorMsg:    "invalid document name",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateCreateDocumentRequest(tt.request)
			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func setupDocumentServiceMocks(t *testing.T) (*documentService, *mockRepos.MockDocumentRepository, *mockRepos.MockDocumentMappingRepository, *mockGdrive.MockDriveClient) {
	mockDocRepo := mockRepos.NewMockDocumentRepository(t)
	mockMappingRepo := mockRepos.NewMockDocumentMappingRepository(t)
	mockDriveClient := mockGdrive.NewMockDriveClient(t)

	service := &documentService{
		documentRepo: mockDocRepo,
		mappingRepo:  mockMappingRepo,
		driveClient:  mockDriveClient,
	}

	return service, mockDocRepo, mockMappingRepo, mockDriveClient
}

func TestCreateDocument_Success(t *testing.T) {
	service, mockDocRepo, mockMappingRepo, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID:  1,
		Name:      "Test Folder",
		DocType:   DocTypeDir,
		ParentID:  "parent123",
	}

	expectedDriveFolder := &drive.File{
		Id:          "folder123",
		Name:        "Test Folder",
		WebViewLink: "https://drive.google.com/folder123",
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		ListFilesWithQuery(mock.AnythingOfType("string")).
		Return([]*drive.File{}, nil).
		Once()

	mockDriveClient.EXPECT().
		CreateFolder("Test Folder", "parent123").
		Return(expectedDriveFolder, nil).
		Once()

	mockDocRepo.EXPECT().
		Transaction(mock.AnythingOfType("*context.timerCtx"), mock.AnythingOfType("func(*gorm.DB) error")).
		Run(func(ctx context.Context, fn func(*gorm.DB) error) {
			// Simulate successful transaction
			fn(&gorm.DB{})
		}).
		Return(nil).
		Once()

	mockDocRepo.EXPECT().
		CreateDoc(mock.AnythingOfType("*context.timerCtx"), mock.AnythingOfType("*model.Document")).
		Return(nil).
		Once()

	mockMappingRepo.EXPECT().
		CreateOrUpdate(mock.AnythingOfType("*context.timerCtx"), mock.AnythingOfType("*model.DocumentMapping")).
		Return(nil).
		Once()

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "folder123", result.DriveFileID)
	assert.Equal(t, "Test Folder", result.Name)
	assert.Equal(t, uint64(1), result.TenantID)
	assert.Equal(t, "parent123", result.ParentDriveID)
	assert.False(t, result.IsFile)

	// Verify all expectations
	mockDocRepo.AssertExpectations(t)
	mockMappingRepo.AssertExpectations(t)
	mockDriveClient.AssertExpectations(t)
}

func TestCreateDocument_ValidationError(t *testing.T) {
	service, _, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID: 0, // Invalid - missing tenant ID
		Name:     "Test Folder",
		DocType:  DocTypeDir,
	}

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeInvalidRequest, docErr.Code)
	assert.Contains(t, docErr.Message, "Invalid request")
}

func TestCreateDocument_DuplicateFolderError(t *testing.T) {
	service, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID: 1,
		Name:     "Test Folder",
		DocType:  DocTypeDir,
		ParentID: "parent123",
	}

	existingFolder := &drive.File{
		Id:   "existing123",
		Name: "Test Folder",
	}

	// Mock expectations - duplicate found
	mockDriveClient.EXPECT().
		ListFilesWithQuery(mock.AnythingOfType("string")).
		Return([]*drive.File{existingFolder}, nil).
		Once()

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeDriveAPIError, docErr.Code)
	assert.Contains(t, docErr.Message, "Failed to check for duplicate folders")

	mockDriveClient.AssertExpectations(t)
}

func TestCreateDocument_DriveAPIError(t *testing.T) {
	service, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID: 1,
		Name:     "Test Folder",
		DocType:  DocTypeDir,
		ParentID: "parent123",
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		ListFilesWithQuery(mock.AnythingOfType("string")).
		Return([]*drive.File{}, nil).
		Once()

	mockDriveClient.EXPECT().
		CreateFolder("Test Folder", "parent123").
		Return(nil, errors.New("Google Drive API error")).
		Once()

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeDatabaseError, docErr.Code)
	assert.Contains(t, docErr.Message, "Failed to create document with transaction")

	mockDriveClient.AssertExpectations(t)
}

func TestCreateDocument_DatabaseTransactionError(t *testing.T) {
	service, mockDocRepo, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID: 1,
		Name:     "Test Folder",
		DocType:  DocTypeDir,
		ParentID: "parent123",
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		ListFilesWithQuery(mock.AnythingOfType("string")).
		Return([]*drive.File{}, nil).
		Once()

	mockDocRepo.EXPECT().
		Transaction(mock.AnythingOfType("*context.timerCtx"), mock.AnythingOfType("func(*gorm.DB) error")).
		Return(errors.New("database transaction failed")).
		Once()

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeDatabaseError, docErr.Code)
	assert.Contains(t, docErr.Message, "Failed to create document with transaction")

	mockDocRepo.AssertExpectations(t)
	mockDriveClient.AssertExpectations(t)
}

func TestCreateDocument_UnsupportedDocType(t *testing.T) {
	service, _, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID: 1,
		Name:     "Test File",
		DocType:  2, // File type - unsupported
		ParentID: "parent123",
	}

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeInvalidRequest, docErr.Code)
	assert.Contains(t, docErr.Message, "Creating files with content is not supported yet")
}
