package gdrive

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/gdrive"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/pkg/logger"
	"google.golang.org/api/drive/v3"
	"gorm.io/gorm"
)

// Validation constants
const (
	MaxDocumentNameLength = 255
	MinDocumentNameLength = 1
	MaxPathDepth         = 10
)

// Invalid characters for document names (Google Drive restrictions)
var invalidChars = []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|", "\x00"}

// Path traversal patterns
var pathTraversalPatterns = []string{"../", "..\\", "..", "~/"}

// Import model constants for document types
// DocTypeDir = 1 (Directory/Folder) from model package
// DocTypeFile = 2 (File) from model package

// DocumentService interface defines operations for managing Google Drive documents
type DocumentService interface {
	// Core CRUD operations (using string IDs for Google Drive file IDs)
	CreateDocument(ctx context.Context, req *CreateDocumentRequest) (*DocumentResponse, error)
	GetDocument(ctx context.Context, driveFileID string) (*DocumentResponse, error)
	UpdateDocument(ctx context.Context, driveFileID string, req *UpdateDocumentRequest) (*DocumentResponse, error)
	DeleteDocument(ctx context.Context, driveFileID string) error

	// ID resolution operations
	GetDocumentByInternalID(ctx context.Context, internalID uint64) (*DocumentResponse, error)

	// File operations
	UploadDocument(ctx context.Context, req *UploadDocumentRequest) (*DocumentResponse, error)
	DownloadDocument(ctx context.Context, driveFileID string) (*DownloadDocumentResponse, error)

	// List and search operations
	ListDocuments(ctx context.Context, req *ListDocumentsRequest) (*DocumentListResponse, error)
	SearchDocuments(ctx context.Context, req *SearchDocumentsRequest) (*DocumentListResponse, error)

	// Getter methods for testing/events
	GetDocumentRepository() repositories.DocumentRepository
	GetDocumentMappingRepository() repositories.DocumentMappingRepository
	GetGDriveClient() gdrive.DriveClient
}

// documentService implements the DocumentService interface
type documentService struct {
	documentRepo repositories.DocumentRepository
	mappingRepo  repositories.DocumentMappingRepository
	driveClient  gdrive.DriveClient
}

// NewDocumentService creates a new instance of DocumentService
func NewDocumentService(
	documentRepo repositories.DocumentRepository,
	mappingRepo repositories.DocumentMappingRepository,
	driveClient gdrive.DriveClient,
) DocumentService {
	return &documentService{
		documentRepo: documentRepo,
		mappingRepo:  mappingRepo,
		driveClient:  driveClient,
	}
}

// GetDocumentRepository returns the document repository for testing/events
func (s *documentService) GetDocumentRepository() repositories.DocumentRepository {
	return s.documentRepo
}

// GetDocumentMappingRepository returns the document mapping repository for testing/events
func (s *documentService) GetDocumentMappingRepository() repositories.DocumentMappingRepository {
	return s.mappingRepo
}

// GetGDriveClient returns the Google Drive client for testing/events
func (s *documentService) GetGDriveClient() gdrive.DriveClient {
	return s.driveClient
}

// validateDocumentName validates document name for security and business rules
func validateDocumentName(name string) error {
	// Check length constraints
	if len(name) < MinDocumentNameLength {
		return fmt.Errorf("document name cannot be empty")
	}
	if len(name) > MaxDocumentNameLength {
		return fmt.Errorf("document name exceeds maximum length of %d characters", MaxDocumentNameLength)
	}

	// Check for invalid characters
	for _, char := range invalidChars {
		if strings.Contains(name, char) {
			return fmt.Errorf("document name contains invalid character: %s", char)
		}
	}

	// Check for path traversal attempts
	lowerName := strings.ToLower(name)
	for _, pattern := range pathTraversalPatterns {
		if strings.Contains(lowerName, pattern) {
			return fmt.Errorf("document name contains invalid path traversal pattern: %s", pattern)
		}
	}

	// Check for leading/trailing whitespace
	if strings.TrimSpace(name) != name {
		return fmt.Errorf("document name cannot have leading or trailing whitespace")
	}

	return nil
}

// validateCreateDocumentRequest validates the entire request
func validateCreateDocumentRequest(req *CreateDocumentRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}

	if req.TenantID == 0 {
		return fmt.Errorf("tenant ID is required")
	}

	if err := validateDocumentName(req.Name); err != nil {
		return fmt.Errorf("invalid document name: %w", err)
	}

	return nil
}

// CreateDocument creates a new document in Google Drive only (no database tracking)
func (s *documentService) CreateDocument(ctx context.Context, req *CreateDocumentRequest) (*DocumentResponse, error) {
	log := logger.WithCtx(ctx, "CreateDocument")
	log.Info("Creating new document in Google Drive")

	// Enhanced input validation
	if err := validateCreateDocumentRequest(req); err != nil {
		log.WithError(err).Error("Request validation failed")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: fmt.Sprintf("Invalid request: %v", err),
		}
	}

	// Set context timeout for external API calls
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Determine parent drive ID
	var parentDriveID string
	if req.ParentID != "" {
		parentDriveID = req.ParentID
		log.WithField("parent_id", req.ParentID).
			WithField("tenant_id", req.TenantID).
			WithField("name", req.Name).
			Info("Using provided parent ID")
	}

	// Handle unsupported document types - fail fast
	if req.DocType != model.DocTypeDir {
		log.Error("Creating files with content is not supported yet")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Creating files with content is not supported yet. Use UploadDocument instead.",
		}
	}

	// Check for duplicate folders in Google Drive
	if err := s.checkDuplicateFolder(ctx, req.Name, parentDriveID); err != nil {
		log.WithError(err).Error("Duplicate folder check failed")
		return nil, err
	}

	// Create folder in Google Drive
	driveFolder, err := s.driveClient.CreateFolder(req.Name, parentDriveID)
	if err != nil {
		log.WithError(err).Error("Failed to create folder in Google Drive")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: fmt.Sprintf("Failed to create folder in Google Drive: %v", err),
		}
	}

	log.WithField("drive_file_id", driveFolder.Id).
		WithField("name", driveFolder.Name).
		Info("Successfully created folder in Google Drive")

	// Return response with Google Drive information only
	return &DocumentResponse{
		DriveFileID:   driveFolder.Id,
		Name:          driveFolder.Name,
		TenantID:      req.TenantID,
		ParentDriveID: parentDriveID,
		WebViewLink:   driveFolder.WebViewLink,
		IsFile:        false,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}, nil
}

// checkDuplicateFolder checks if a folder with the same name already exists in the parent
func (s *documentService) checkDuplicateFolder(ctx context.Context, folderName, parentID string) error {
	log := logger.WithCtx(ctx, "checkDuplicateFolder")

	// Use Google Drive API to search for existing folders
	query := fmt.Sprintf("name='%s' and parents in '%s' and mimeType='application/vnd.google-apps.folder' and trashed=false",
		strings.ReplaceAll(folderName, "'", "\\'"), parentID)

	files, err := s.driveClient.ListFilesWithQuery(query)
	if err != nil {
		return fmt.Errorf("failed to search for existing folders: %w", err)
	}

	if len(files) > 0 {
		log.WithField("existing_folder_id", files[0].Id).
			WithField("folder_name", folderName).
			WithField("parent_id", parentID).
			Warn("Folder with same name already exists")
		return fmt.Errorf("folder with name '%s' already exists in parent folder", folderName)
	}

	return nil
}

// executeCreateDocumentTransaction executes the document creation with proper transaction management
func (s *documentService) executeCreateDocumentTransaction(ctx context.Context, req *CreateDocumentRequest, parentDriveID string) (*DocumentResponse, error) {
	log := logger.WithCtx(ctx, "executeCreateDocumentTransaction")

	var driveFolder *drive.File
	var document *model.Document
	var documentMapping *model.DocumentMapping
	var err error

	// Start database transaction using repository's Transaction method
	err = s.documentRepo.Transaction(ctx, func(tx *gorm.DB) error {
		// Step 1: Create folder in Google Drive
		driveFolder, err = s.driveClient.CreateFolder(req.Name, parentDriveID)
		if err != nil {
			log.WithError(err).
				WithField("name", req.Name).
				WithField("parent_id", parentDriveID).
				Error("Failed to create folder in Google Drive")
			return fmt.Errorf("failed to create folder in Google Drive: %w", err)
		}

		log.WithField("drive_folder_id", driveFolder.Id).
			WithField("name", driveFolder.Name).
			Info("Successfully created folder in Google Drive")

		// Step 2: Create Document record in database
		document = &model.Document{
			TenantID:   req.TenantID,
			Name:       driveFolder.Name,
			ObjectID:   req.ObjectID,
			DocType:    2, // Default folder type
			Status:     1, // Active status
		}

		if err = s.documentRepo.CreateDoc(ctx, document); err != nil {
			log.WithError(err).Error("Failed to create document record")
			// Cleanup Google Drive folder on database failure
			if deleteErr := s.driveClient.DeleteFile(driveFolder.Id); deleteErr != nil {
				log.WithError(deleteErr).Error("Failed to cleanup Google Drive folder after database error")
			}
			return fmt.Errorf("failed to create document record: %w", err)
		}

		log.WithField("document_id", document.ID).Info("Successfully created document record")

		return nil
	})

	if err != nil {
		return nil, &DocumentServiceError{
			Code:    ErrCodeDatabaseError,
			Message: "Failed to create document with transaction",
			Details: err.Error(),
		}
	}

	// Step 3: Create DocumentMapping record (outside main transaction for flexibility)
	documentMapping = &model.DocumentMapping{
		TenantID:  req.TenantID,
		DriveID:   driveFolder.Id,
		Provider:  "gdrive",
		Type:      strconv.Itoa(req.DocType),
		ObjectID:  req.ObjectID,
	}

	if err = s.mappingRepo.CreateOrUpdate(ctx, documentMapping); err != nil {
		log.WithError(err).Error("Failed to create document mapping")
		// Note: We don't rollback the main transaction here as it's already committed
		// This is a design decision - the document exists but mapping failed
		// Could be handled by a background job or manual intervention
	}

	// Success path - return complete response
	return &DocumentResponse{
		DriveFileID:   driveFolder.Id,
		Name:          driveFolder.Name,
		TenantID:      req.TenantID,
		ParentDriveID: parentDriveID,
		WebViewLink:   driveFolder.WebViewLink,
		IsFile:        false,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}, nil
}

// getParentFolderIDFromObject queries the document mapping repository to find the parent folder ID
// for a given object type and object ID
func (s *documentService) getParentFolderIDFromObject(ctx context.Context, objectType string, objectID uint64, tenantID uint64) (string, error) {
	log := logger.WithCtx(ctx, "getParentFolderIDFromObject")
	log.WithField("object_type", objectType).
		WithField("object_id", objectID).
		Info("Finding parent folder using object mapping")

	// Validate object type - fail fast
	var docType string
	switch objectType {
	case "client":
		docType = model.DocTypeClient
	case "matter":
		docType = model.DocTypeMatter
	default:
		log.WithField("object_type", objectType).Error("Unsupported object type")
		return "", &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Unsupported object type: " + objectType,
		}
	}

	// Build query
	query := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("type", docType),
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("object_id", objectID),
			model.NewFilterE("provider", model.DocProviderGoogle),
		},
	}

	// Find parent folder mapping
	mapping, err := s.mappingRepo.FindOne(ctx, query)
	if err != nil {
		log.WithError(err).
			WithField("object_type", objectType).
			WithField("object_id", objectID).
			Error("Failed to find parent folder mapping")
		return "", &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Failed to find parent folder for " + objectType + " with ID " + fmt.Sprint(objectID),
			Details: err.Error(),
		}
	}

	if mapping == nil {
		log.WithField("object_type", objectType).
			WithField("object_id", objectID).
			Error("Parent folder mapping not found")
		return "", &DocumentServiceError{
			Code:    ErrCodeDocumentNotFound,
			Message: "Parent folder for " + objectType + " with ID " + fmt.Sprint(objectID) + " not found",
		}
	}

	// Success path - main flow
	log.WithField("object_type", objectType).
		WithField("object_id", objectID).
		WithField("parent_drive_id", mapping.DriveID).
		Info("Found parent folder drive ID from mapping")

	return mapping.DriveID, nil
}

// GetDocument retrieves a document by its Google Drive file ID
func (s *documentService) GetDocument(ctx context.Context, driveFileID string) (*DocumentResponse, error) {
	log := logger.WithCtx(ctx, "GetDocument")
	log.WithField("driveFileID", driveFileID).Info("Getting document")

	// Validate input
	if driveFileID == "" {
		log.Error("Missing Drive file ID")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing Drive file ID",
		}
	}

	// Get file info from Google Drive
	driveFile, err := s.driveClient.GetFileInfo(driveFileID)
	if err != nil {
		log.WithError(err).Error("Failed to get file from Google Drive")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: "Failed to get file from Google Drive",
			Details: err.Error(),
		}
	}

	// Initialize response with Google Drive data
	response := &DocumentResponse{
		DriveFileID:  driveFile.Id,
		Name:         driveFile.Name,
		ContentType:  driveFile.MimeType,
		WebViewLink:  driveFile.WebViewLink,
		DownloadLink: driveFile.WebContentLink,
		IsFile:       driveFile.MimeType != gdrive.MimeTypeFolder,
	}

	// Check if we have a mapping for this file (for client/matter folders)
	mappingQuery := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("drive_id", driveFileID),
			model.NewFilterE("provider", model.DocProviderGoogle),
		},
	}

	mapping, err := s.mappingRepo.FindOne(ctx, mappingQuery)
	if err == nil && mapping != nil {
		// If we have a mapping, get the document from database
		docQuery := &model.Query{
			Filters: []*model.Filter{
				model.NewFilterE("id", mapping.ObjectID),
			},
		}

		doc, err := s.documentRepo.FindOne(ctx, docQuery)
		if err == nil && doc != nil {
			// Enhance response with database information
			response.ID = doc.ID
			response.TenantID = doc.TenantID
			response.ParentID = doc.ParentID
			response.Size = doc.Size
			response.CreatedAt = doc.CreatedAt
			response.UpdatedAt = doc.UpdatedAt
			response.CreatedBy = doc.CreatedUser
			response.UpdatedBy = doc.UpdatedUser
		}

		// Add mapping data
		response.ParentDriveID = mapping.ParentDriveID
	}

	log.WithField("driveFileID", driveFileID).Info("Document retrieved successfully")
	return response, nil
}

// GetDocumentByInternalID retrieves a document by its internal database ID
func (s *documentService) GetDocumentByInternalID(ctx context.Context, internalID uint64) (*DocumentResponse, error) {
	log := logger.WithCtx(ctx, "GetDocumentByInternalID")
	log.WithField("internalID", internalID).Info("Getting document by internal ID")

	// Get document from database
	docQuery := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("id", internalID),
		},
	}

	doc, err := s.documentRepo.FindOne(ctx, docQuery)
	if err != nil {
		log.WithError(err).Error("Failed to find document in database")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDocumentNotFound,
			Message: "Document not found in database",
			Details: err.Error(),
		}
	}

	// Get mapping to find Google Drive ID
	mappingQuery := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("object_id", doc.ID),
			model.NewFilterE("provider", model.DocProviderGoogle),
		},
	}

	mapping, err := s.mappingRepo.FindOne(ctx, mappingQuery)
	if err != nil {
		log.WithError(err).Error("Failed to find document mapping")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDocumentNotFound,
			Message: "Document mapping not found",
			Details: err.Error(),
		}
	}

	// Get file info from Google Drive
	driveFile, err := s.driveClient.GetFileInfo(mapping.DriveID)
	if err != nil {
		log.WithError(err).Error("Failed to get file from Google Drive")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: "Failed to get file from Google Drive",
			Details: err.Error(),
		}
	}

	// Build response with combined information
	response := &DocumentResponse{
		ID:            doc.ID,
		DriveFileID:   mapping.DriveID,
		Name:          doc.Name,
		TenantID:      doc.TenantID,
		ParentID:      doc.ParentID,
		ParentDriveID: mapping.ParentDriveID,
		Size:          doc.Size,
		ContentType:   driveFile.MimeType,
		WebViewLink:   driveFile.WebViewLink,
		DownloadLink:  driveFile.WebContentLink,
		IsFile:        doc.DocType == model.DocTypeFile,
		CreatedAt:     doc.CreatedAt,
		UpdatedAt:     doc.UpdatedAt,
		CreatedBy:     doc.CreatedUser,
		UpdatedBy:     doc.UpdatedUser,
	}

	log.WithField("internalID", internalID).Info("Document retrieved successfully")
	return response, nil
}

// UpdateDocument updates an existing document by its Google Drive file ID
func (s *documentService) UpdateDocument(ctx context.Context, driveFileID string, req *UpdateDocumentRequest) (*DocumentResponse, error) {
	log := logger.WithCtx(ctx, "UpdateDocument")
	log.WithField("driveFileID", driveFileID).Info("Updating document")

	// Validate input
	if driveFileID == "" {
		log.Error("Missing Drive file ID")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing Drive file ID",
		}
	}

	// Get file info from Google Drive
	driveFile, err := s.driveClient.GetFileInfo(driveFileID)
	if err != nil {
		log.WithError(err).Error("Failed to get file from Google Drive")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: "Failed to get file from Google Drive",
			Details: err.Error(),
		}
	}

	// Update file in Google Drive if name has changed
	if req.Name != "" && req.Name != driveFile.Name {
		driveFile, err = s.driveClient.RenameFile(driveFileID, req.Name)
		if err != nil {
			log.WithError(err).Error("Failed to rename file in Google Drive")
			return nil, &DocumentServiceError{
				Code:    ErrCodeDriveAPIError,
				Message: "Failed to rename file in Google Drive",
				Details: err.Error(),
			}
		}
	}

	// For folders inside client/matter, we don't store mappings
	// Try to find mapping, but don't fail if not found
	var doc *model.Document

	// Try to find document mapping
	mappingQuery := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("drive_id", driveFileID),
			model.NewFilterE("provider", model.DocProviderGoogle),
		},
	}

	mapping, err := s.mappingRepo.FindOne(ctx, mappingQuery)
	if err == nil {
		// If mapping exists, update document in database
		docQuery := &model.Query{
			Filters: []*model.Filter{
				model.NewFilterE("id", mapping.ObjectID),
			},
		}

		// Get original document first
		doc, _ = s.documentRepo.FindOne(ctx, docQuery)
		if doc != nil {
			// Update document
			docUpdate := &model.Document{
				UpdatedUser: getCurrentUserID(ctx),
			}

			if req.Name != "" {
				docUpdate.Name = req.Name
			}

			if req.Size > 0 {
				docUpdate.Size = req.Size
			}

			// Update document in database
			err = s.documentRepo.UpdateOne(ctx, docQuery, docUpdate)
			if err != nil {
				log.WithError(err).Error("Failed to update document in database")
				// Continue anyway since Drive file was updated successfully
			}
		}
	}

	log.WithField("driveFileID", driveFileID).Info("Document updated successfully")

	// Build and return response based on what we know
	response := &DocumentResponse{
		DriveFileID:  driveFile.Id,
		Name:         driveFile.Name,
		ContentType:  driveFile.MimeType,
		WebViewLink:  driveFile.WebViewLink,
		DownloadLink: driveFile.WebContentLink,
		IsFile:       driveFile.MimeType != gdrive.MimeTypeFolder,
	}

	// Add internal data if available
	if doc != nil {
		response.ID = doc.ID
		response.TenantID = doc.TenantID
		response.ParentID = doc.ParentID
		response.Size = doc.Size
		response.CreatedAt = doc.CreatedAt
		response.UpdatedAt = doc.UpdatedAt
		response.CreatedBy = doc.CreatedUser
		response.UpdatedBy = doc.UpdatedUser
	}

	// Add mapping data if available
	if mapping != nil {
		response.ParentDriveID = mapping.ParentDriveID
	}

	return response, nil
}

// DeleteDocument deletes a document by its Google Drive file ID
func (s *documentService) DeleteDocument(ctx context.Context, driveFileID string) error {
	log := logger.WithCtx(ctx, "DeleteDocument")
	log.WithField("driveFileID", driveFileID).Info("Deleting document")

	// Validate input
	if driveFileID == "" {
		log.Error("Missing Drive file ID")
		return &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing Drive file ID",
		}
	}

	// Try to find document mapping
	mappingQuery := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("drive_id", driveFileID),
			model.NewFilterE("provider", model.DocProviderGoogle),
		},
	}

	mapping, err := s.mappingRepo.FindOne(ctx, mappingQuery)
	if err == nil && mapping != nil {
		// If mapping exists, delete document from database
		docQuery := &model.Query{
			Filters: []*model.Filter{
				model.NewFilterE("id", mapping.ObjectID),
			},
		}

		// Delete document from database
		err = s.documentRepo.Delete(ctx, docQuery)
		if err != nil {
			log.WithError(err).Error("Failed to delete document from database")
			// Continue to delete from Drive anyway
		}

		// Delete mapping
		err = s.mappingRepo.Delete(ctx, mappingQuery)
		if err != nil {
			log.WithError(err).Error("Failed to delete document mapping")
			// Continue to delete from Drive anyway
		}
	}

	// Delete file from Google Drive
	err = s.driveClient.DeleteFile(driveFileID)
	if err != nil {
		log.WithError(err).Error("Failed to delete file from Google Drive")
		return &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: "Failed to delete file from Google Drive",
			Details: err.Error(),
		}
	}

	log.WithField("driveFileID", driveFileID).Info("Document deleted successfully")
	return nil
}

// UploadDocument uploads a document to Google Drive
func (s *documentService) UploadDocument(ctx context.Context, req *UploadDocumentRequest) (*DocumentResponse, error) {
	log := logger.WithCtx(ctx, "UploadDocument")
	log.Info("Uploading document")

	// Validate request
	if req.TenantID == 0 {
		log.Error("Missing tenant ID")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing tenant ID",
		}
	}

	if req.ParentDriveID == "" {
		log.Error("Missing parent folder ID")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing parent folder ID",
		}
	}

	if req.FileName == "" {
		log.Error("Missing file name")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing file name",
		}
	}

	if req.Content == nil {
		log.Error("Missing file content")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing file content",
		}
	}

	// Create a temporary file path for upload
	// In a real implementation, we would write the content to a temp file
	tempFilePath := "/tmp/" + req.FileName

	// Upload to Google Drive using the UploadFileToSharedDriveFolder method
	driveFile, err := s.driveClient.UploadFileToSharedDriveFolder(
		req.FileName,
		tempFilePath, // This is a placeholder, would be real in production
		"",           // We're not using shared drives for regular uploads
		req.ParentDriveID,
	)
	if err != nil {
		log.WithError(err).Error("Failed to upload file to Google Drive")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: "Failed to upload file to Google Drive",
			Details: err.Error(),
		}
	}

	// Build and return response
	response := &DocumentResponse{
		DriveFileID:   driveFile.Id,
		Name:          driveFile.Name,
		TenantID:      req.TenantID,
		ContentType:   driveFile.MimeType,
		WebViewLink:   driveFile.WebViewLink,
		DownloadLink:  driveFile.WebContentLink,
		ParentDriveID: req.ParentDriveID,
		IsFile:        true,
		Size:          req.Size,
	}

	log.WithField("driveFileID", driveFile.Id).Info("Document uploaded successfully")
	return response, nil
}

// DownloadDocument downloads a document by its Google Drive file ID
func (s *documentService) DownloadDocument(ctx context.Context, driveFileID string) (*DownloadDocumentResponse, error) {
	log := logger.WithCtx(ctx, "DownloadDocument")
	log.WithField("driveFileID", driveFileID).Info("Downloading document")

	// Validate input
	if driveFileID == "" {
		log.Error("Missing Drive file ID")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing Drive file ID",
		}
	}

	// Get file info from Google Drive
	driveFile, err := s.driveClient.GetFileInfo(driveFileID)
	if err != nil {
		log.WithError(err).Error("Failed to get file from Google Drive")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: "Failed to get file from Google Drive",
			Details: err.Error(),
		}
	}

	// Create a temporary file path for download
	tempFilePath := "/tmp/" + driveFile.Name

	// Download file content from Google Drive
	err = s.driveClient.DownloadFile(driveFileID, tempFilePath)
	if err != nil {
		log.WithError(err).Error("Failed to download file from Google Drive")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: "Failed to download file from Google Drive",
			Details: err.Error(),
		}
	}

	// In a real implementation, we would read the content from the temp file
	// and return it as an io.ReadCloser
	// For now, we'll just return a placeholder response

	// Build and return response
	response := &DownloadDocumentResponse{
		ContentType: driveFile.MimeType,
		FileName:    driveFile.Name,
		Size:        driveFile.Size,
	}

	log.WithField("driveFileID", driveFileID).Info("Document downloaded successfully")
	return response, nil
}

// ListDocuments lists documents in a folder
func (s *documentService) ListDocuments(ctx context.Context, req *ListDocumentsRequest) (*DocumentListResponse, error) {
	log := logger.WithCtx(ctx, "ListDocuments")
	log.WithField("parentDriveID", req.ParentDriveID).Info("Listing documents")

	// Validate request
	if req.TenantID == 0 {
		log.Error("Missing tenant ID")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing tenant ID",
		}
	}

	// Use "root" if no parent folder ID is specified
	parentID := req.ParentDriveID
	if parentID == "" {
		parentID = "root"
	}

	// Set up pagination options
	pageSize := int64(req.PageSize)
	if pageSize <= 0 {
		pageSize = 100
	}

	// List files from Google Drive
	paginationOpts := &gdrive.PaginationOptions{
		PageSize:  pageSize,
		PageToken: req.PageToken,
	}

	var result *gdrive.PaginatedResult
	var err error

	// Use the appropriate method based on the parent ID
	if parentID == "root" {
		result, err = s.driveClient.ListFiles(paginationOpts)
	} else {
		result, err = s.driveClient.ListFilesInFolder(parentID, paginationOpts)
	}

	if err != nil {
		log.WithError(err).Error("Failed to list files from Google Drive")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: "Failed to list files from Google Drive",
			Details: err.Error(),
		}
	}

	// Build response
	documents := make([]*DocumentResponse, 0, len(result.Files))
	for _, file := range result.Files {
		doc := &DocumentResponse{
			DriveFileID:   file.Id,
			Name:          file.Name,
			TenantID:      req.TenantID,
			ContentType:   file.MimeType,
			WebViewLink:   file.WebViewLink,
			DownloadLink:  file.WebContentLink,
			ParentDriveID: parentID,
			IsFile:        file.MimeType != gdrive.MimeTypeFolder,
		}
		documents = append(documents, doc)
	}

	log.WithField("count", len(documents)).Info("Documents listed successfully")
	return &DocumentListResponse{
		Documents:     documents,
		NextPageToken: result.NextPageToken,
		Total:         len(documents),
	}, nil
}

// SearchDocuments searches for documents
func (s *documentService) SearchDocuments(ctx context.Context, req *SearchDocumentsRequest) (*DocumentListResponse, error) {
	log := logger.WithCtx(ctx, "SearchDocuments")
	log.WithField("query", req.Query).Info("Searching documents")

	// Validate request
	if req.TenantID == 0 {
		log.Error("Missing tenant ID")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing tenant ID",
		}
	}

	if req.Query == "" {
		log.Error("Missing search query")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing search query",
		}
	}

	// Search files in Google Drive using ListFilesWithQuery
	files, err := s.driveClient.ListFilesWithQuery(req.Query)
	if err != nil {
		log.WithError(err).Error("Failed to search files in Google Drive")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: "Failed to search files in Google Drive",
			Details: err.Error(),
		}
	}

	// Build response
	documents := make([]*DocumentResponse, 0, len(files))
	for _, file := range files {
		doc := &DocumentResponse{
			DriveFileID:  file.Id,
			Name:         file.Name,
			TenantID:     req.TenantID,
			ContentType:  file.MimeType,
			WebViewLink:  file.WebViewLink,
			DownloadLink: file.WebContentLink,
			IsFile:       file.MimeType != gdrive.MimeTypeFolder,
		}
		documents = append(documents, doc)
	}

	log.WithField("count", len(documents)).Info("Documents searched successfully")
	return &DocumentListResponse{
		Documents:     documents,
		NextPageToken: "", // ListFilesWithQuery doesn't support pagination yet
		Total:         len(documents),
	}, nil
}

// Helper functions for document service

// getObjectType determines the object type based on client and matter IDs
func getObjectType(clientID, matterID uint64) int {
	if matterID > 0 {
		return 3 // Matter
	} else if clientID > 0 {
		return 2 // Client
	}
	return 1 // Default (Tenant)
}

// getObjectID returns the appropriate object ID based on client and matter IDs
func getObjectID(clientID, matterID uint64) uint64 {
	if matterID > 0 {
		return matterID
	} else if clientID > 0 {
		return clientID
	}
	return 0
}

// getCurrentUserID extracts the user ID from context
// In a real implementation, this would get the authenticated user's ID
func getCurrentUserID(ctx context.Context) uint64 {
	// TODO: Get actual user ID from context
	return 1 // Default user ID for now
}
